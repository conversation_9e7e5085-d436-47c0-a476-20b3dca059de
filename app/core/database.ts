import { tap } from '@kdt310722/utils/function'
import { DataSource } from 'typeorm'
import { config } from '../config'
import { DatabaseLogger } from '../utils/database/logger'
import { NamingStrategy } from '../utils/database/naming-strategy'
import { appPath } from '../utils/path'
import { createChildLogger } from './logger'

const logger = createChildLogger('core:database')

export const database = new DataSource({
    ...config.database,
    entities: [appPath('entities', '*.ts'), appPath('modules/*/entities', '*.ts')],
    logger: new DatabaseLogger(logger, config.database.logging),
    namingStrategy: new NamingStrategy(),
})

export async function initializeDatabase() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing database...'))

    return database.initialize().then(() => {
        logger.stopTimer(timer, 'info', 'Database initialized!')
    })
}
