import type { Logger } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'

async function main(logger: Logger) {
    const timer = tap(logger.createTimer(), () => logger.info('Starting application...'))

    await import('./core/database').then((m) => m.initializeDatabase())

    logger.stopTimer(timer, 'info', 'Application started!')
}

import('./core/logger').then(({ logger }) => main(logger).catch((error) => {
    logger.forceExit(1, 'fatal', 'Failed to start application', error)
}))
