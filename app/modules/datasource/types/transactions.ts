import type { CompiledInstruction } from './instructions'

export interface BaseTransaction {
    slot: number
    signature: string
    receivedAt: number
}

export interface TransactionWithLogs extends BaseTransaction {
    logs: string[]
}

export interface TransactionWithInstructions extends BaseTransaction {
    instructions: CompiledInstruction[]
}

export type Transaction = TransactionWithLogs | TransactionWithInstructions
