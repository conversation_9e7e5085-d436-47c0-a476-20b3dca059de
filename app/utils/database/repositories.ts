import { chunk } from '@kdt310722/utils/array'
import { notNullish } from '@kdt310722/utils/common'
import type { InsertResult, ObjectLiteral, Repository } from 'typeorm'
import { config } from '../../config'

export enum ConflictType {
    IGNORE = 'IGNORE',
    UPDATE = 'UPDATE',
}

export interface UpsertOptions {
    conflictType?: ConflictType
    comment?: string
}

export async function upsert<E extends ObjectLiteral>(repository: Repository<E>, data: E | E[], conflictPaths: Array<Extract<keyof E, string> | string>, { comment, conflictType = ConflictType.UPDATE }: UpsertOptions = {}) {
    const query = repository.createQueryBuilder().insert().into(repository.target).values(data).returning(conflictPaths)

    if (notNullish(comment)) {
        query.comment(comment)
    }

    if (conflictType === ConflictType.UPDATE) {
        query.orUpdate(conflictPaths, conflictPaths, { upsertType: 'on-conflict-do-update' })
    } else {
        query.orIgnore()
    }

    return query.execute()
}

export interface ChunkUpsertOptions extends UpsertOptions {
    chunkSize?: number
}

export async function chunkUpsert<E extends ObjectLiteral>(repository: Repository<E>, dataset: E[], conflictPaths: Array<Extract<keyof E, string> | string>, { chunkSize = config.database.defaultChunkSize, ...options }: ChunkUpsertOptions = {}) {
    const result: InsertResult[] = []
    const chunks = chunk(dataset, chunkSize)

    for (const data of chunks) {
        result.push(await upsert(repository, data, conflictPaths, options))
    }

    return result
}
