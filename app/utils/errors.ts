import { BaseError } from '../errors/base-error'

export function isAbortError(error: unknown): error is Error & { name: 'AbortError' } {
    return error instanceof Error && error.name === 'AbortError'
}

export function createAbortError(message = 'The operation was aborted', options?: ErrorOptions) {
    return Object.assign(new Error(message, options), { name: 'AbortError' })
}

export function isRetryableError(error: unknown) {
    if (error instanceof BaseError) {
        return error.retryable !== false
    }

    return !(error instanceof Error) || !isAbortError(error)
}
