Dự án này là 1 bot cli hỗ trợ tự động mua / bán 1 token mới được niêm yết trên blockchain Solana. Mục tiêu của dự án là có thể mua được token 1 cách nhanh nhất có thể, ngay sau giao dịch thêm thanh khoản của người tạo token, nhằm tối đa hóa lợi nhuận kiếm được. Bot này cũng hỗ trợ tính năng tự động bán theo các điều kiện được cài đặt trước như TP, SL,...

# Tính năng

- Dự án áp dụng Adapter <PERSON>tern, giúp dễ dàng mở rộng và thêm các sàn / dịch vụ gửi giao dịch / transaction streaming service khác sau này
- <PERSON> dõi các token mới được niêm yết trên các DEX hỗ trợ
- <PERSON><PERSON><PERSON> token theo các điều kiện
- <PERSON><PERSON> ngay sau khi token được niêm yết và đạt đủ điều kiện
- Tự động bán theo các strategy
- Hiển thị / thống kê lãi / lỗ
- Hỗ trợ nhiều dịch vụ gửi giao dịch như: Jito, NextBlock, BloXRoute, Temporal, 0Slot, etc
- Hỗ trợ nhiều dịch vụ transaction streaming như: YellowStone Geyser GRPC, Shreder.xyz, ThorStreamer, Orbit Jetstream, Corvus ARPC,...

# Các DEX hỗ trợ

- Pump.Fun (Đang phát triển)
- Raydium LaunchLab (Chưa hỗ trợ)
- Boop.Fun (Chưa hỗ trợ)
- Meteora Dynamic Bonding Curve (Chưa hỗ trợ)
