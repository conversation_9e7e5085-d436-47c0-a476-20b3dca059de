{"type": "module", "private": true, "packageManager": "pnpm@10.11.0", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=20.12.0"}, "scripts": {"dev": "node bin/run.js", "start": "NODE_ENV=production node bin/run.js", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks"}, "dependencies": {"@kdt-sol/pumpfun-sdk": "^0.3.0", "@kdt-sol/solana-grpc-client": "^0.1.2", "@kdt310722/config": "^0.0.4", "@kdt310722/logger": "^0.0.12", "@kdt310722/utils": "^0.0.17", "@solana/kit": "^2.1.1", "better-sqlite3": "^11.10.0", "pg": "^8.16.0", "pluralize": "^8.0.0", "typeorm": "^0.3.24", "typeorm-naming-strategies": "^4.1.0", "zod": "^3.25.30", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt310722/eslint-config": "^0.2.0", "@kdt310722/tsconfig": "^1.0.0", "@types/node": "^22.15.21", "@types/pluralize": "^0.0.33", "eslint": "^9.27.0", "lint-staged": "^16.0.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "simple-git-hooks": "^2.13.0", "ts-node-maintained": "^10.9.5", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx tsc --noEmit && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}